<template>
  <view class="web">
    <view class="deviceInfo">
      <view class="car-img">
        <carImg :vehicleType="'forklift'" />
      </view>
      <view class="car-info">
        <view class="no">{{ "车辆001" }}</view>
        <view class="sn">设备编号：{{ basicInfo?.bmsInfo.sn }}</view>
      </view>
    </view>
    <view class="content">
      <scroll-view
        class="scroll-view"
        :scroll-left="scrollLeft"
        :scroll-into-view="scrollIntoView"
      >
        <view class="tabs">
          <view
            v-for="(item, index) in tabOptions"
            :key="item.value"
            class="tab"
            :class="[activeKey == item.id ? 'selected' : '']"
            @click="onChange(item.id, index)"
            :id="'tab' + item.id"
            >{{ item.label }}</view
          >
        </view>
      </scroll-view>
      <view class="swiper-box">
        <swiper
          class="swiper"
          :duration="duration"
          :current-item-id="activeKey"
          @change="onSwiperChange"
          :style="{ height: swiperHeight + 'px' }"
        >
          <swiper-item item-id="A">
            <view class="sw-item" id="slide-A">
              <view class="real-info">
                <view class="device-state">
                  <div
                    class="flex items-center gap-x-1"
                    :style="{
                      color: getState(realData?.status || 0, 'power').color,
                    }"
                  >
                    <i
                      :class="[
                        'iconfont',
                        getState(realData?.status || 0, 'power').icon,
                      ]"
                      :style="{
                        color: getState(realData?.status || 0, 'power').color,
                      }"
                    ></i
                    ><span class="text-title dark:text-title-dark">{{
                      getState(realData?.status || 0, "power").label
                        ? getState(realData?.status || 0, "power").label
                        : "-"
                    }}</span>
                  </div>
                </view>
                <view class="flex items-center">
                  <img src="@/static/4g.svg" class="iconSvg iconfont" />
                  <text class="ml-2"
                    >信号强度：{{ realData?.signal4g || "-" }}</text
                  >
                </view>
                <view
                  v-if="basicInfo?.productModel?.bmsSummaryInfo.totalAlarms"
                >
                  <view class="flex items-center" style="color: #fd0b0b">
                    <i
                      class="iconfont error icon-ica-dianchi-guzhang"
                      style="color: #fd0b0b"
                    ></i>
                    <text class="ml-2"
                      >异常
                      {{
                        basicInfo?.productModel?.bmsSummaryInfo.totalAlarms
                      }}条</text
                    >
                  </view>
                </view>
              </view>
              <view class="guage-chart">
                <!-- #ifdef WEB -->
                <gauge :value="29" />
                <!-- #endif -->
                <!-- #ifdef MP-WEIXIN -->
                <gauge1 :soc="realData?.soc" />
                <!-- #endif -->
              </view>
              <view class="timeAndAddress flex items-center">
                <view class="flex items-center">
                  <img src="@/static/time.svg" class="iconSvg iconfont" />
                  <text class="ml-2">{{
                    dayjs(realData?.time * 1000).format("YYYY/MM/DD HH:mm:ss")
                  }}</text>
                </view>
                <view class="flex items-center ml-5">
                  <img src="@/static/dingwei.svg" class="iconSvg" />
                  <text class="ml-2">{{ realData?.address || "-" }}</text>
                </view>
              </view>
              <view class="statistic">
                <view class="statistic-content">
                  <view class="statistic-item">
                    <view class="num">{{ realData?.sysVoltage }}</view>
                    <view class="name"> 总电压(V)</view>
                  </view>
                  <view class="statistic-item">
                    <view class="num">{{ realData?.sysCurrent }}</view>
                    <view class="name"> 总电流(A) </view>
                  </view>
                  <view class="statistic-item">
                    <view class="num">{{ realData?.soh }}% </view>
                    <view class="name"> 电池健康度 </view>
                  </view>
                </view>
              </view>
              <view class="bg-line"></view>
              <view class="dis-title">
                <view class="mt-6 mb-3">电芯单体详情</view>
                <!-- 电压 -->
                <view>
                  <view class="dis-sub-title">电压热力分布</view>
                  <view class="distributed-chart">
                    <distributed :data="realCells?.voltage" />
                  </view>
                  <view class="distributed">
                    <view class="distributed-content">
                      <view class="distributed-item">
                        <view class="num">{{ realData?.volAvg }}V</view>
                        <view class="name">平均值</view>
                      </view>
                      <view class="distributed-item">
                        <view class="num"
                          >{{ realData?.volMax }}V/#{{
                            realData?.volMaxId
                          }}</view
                        >
                        <view class="name"> 最大值/电芯编号 </view>
                      </view>
                      <view class="distributed-item">
                        <view class="num"
                          >{{ realData?.volMin }}V/#{{
                            realData?.volMinId
                          }}</view
                        >
                        <view class="name"> 最小值/电芯编号 </view>
                      </view>
                    </view>
                  </view>
                </view>
                <!-- 温度 -->
                <view class="mt-8">
                  <view class="dis-sub-title">温度热力分布</view>
                  <view class="distributed-chart-temp">
                    <temp-distributed :data="realCells?.temperature" />
                  </view>
                  <view class="distributed">
                    <view class="distributed-content">
                      <view class="distributed-item">
                        <view class="num">{{ realData?.tempAvg }}°C</view>
                        <view class="name">平均值</view>
                      </view>
                      <view class="distributed-item">
                        <view class="num"
                          >{{ realData?.tempMax }}°C/#{{
                            realData?.tempMaxId
                          }}</view
                        >
                        <view class="name"> 最大值/传感器编号 </view>
                      </view>
                      <view class="distributed-item">
                        <view class="num"
                          >{{ realData?.tempMin }}°C/#{{
                            realData?.tempMinId
                          }}</view
                        >
                        <view class="name"> 最小值/传感器编号 </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </swiper-item>
          <swiper-item item-id="B">
            <view class="" id="slide-B">
              <view class="basic-info">
                <view class="info-box">
                  <view class="title b"> 服务商：{{ "万家乐公司" }} </view>
                  <view class="title b mb-2">
                    客户名称：{{
                      basicInfo?.productModel?.majorCustomers.join("、")
                    }}
                  </view>
                  <view class="title">
                    累计循环次数：{{ realData?.cycleCount }}
                  </view>
                  <view class="title">
                    累计充电时间：{{
                      basicInfo?.productModel?.bmsSummaryInfo?.chgTimeSum
                    }}
                    h
                  </view>
                  <view class="title">
                    累计放电时间：{{
                      basicInfo?.productModel?.bmsSummaryInfo?.dsgTimeSum
                    }}
                    h
                  </view>
                  <view class="title">
                    累计充电容量：{{
                      basicInfo?.productModel?.bmsSummaryInfo?.chgCapSum
                    }}
                    Ah
                  </view>
                  <view class="title">
                    累计放电容量：{{
                      basicInfo?.productModel?.bmsSummaryInfo?.dsgCapSum
                    }}
                    Ah
                  </view>
                </view>
                <view class="info-box">
                  <view class="title b mb-2"> 电芯信息 </view>
                  <view class="title"> 车辆类型：{{ vehicleType }} </view>
                  <view class="title">
                    电芯类型：{{ basicInfo?.productModel?.cellType }}
                  </view>
                  <view class="title">
                    电芯封装：{{ basicInfo?.productModel?.cellPack }}
                  </view>
                  <view class="title">
                    电芯串数：{{ realData?.cellNub || 0 }}
                  </view>
                  <view class="title">
                    电池箱数：{{ basicInfo?.productModel?.tanks }}
                  </view>
                  <view class="title">
                    单体电压：{{ basicInfo?.productModel?.cellVoltage }} V</view
                  >
                  <view class="title">
                    额定总压：{{
                      basicInfo?.productModel?.ratedVoltage
                    }}
                    V</view
                  >
                  <view class="title">
                    额定总流：{{
                      basicInfo?.productModel?.ratedCurrent
                    }}
                    A</view
                  >
                  <view class="title">
                    额定容量：{{
                      basicInfo.productModel?.ratedCapacity || 0
                    }}
                    Ah</view
                  >
                  <view class="title">
                    额定能量：{{
                      basicInfo?.productModel?.ratedEnergy
                    }}
                    kWh</view
                  >
                </view>
                <view class="info-box">
                  <view class="title b mb-2"> 基础信息 </view>
                  <view class="title">
                    设备编号：{{ basicInfo?.bmsInfo?.sn }}
                  </view>
                  <view class="title">
                    电池编号：{{ basicInfo?.bmsInfo?.batteryNo || "-" }}
                  </view>
                  <view class="title">
                    设备型号：{{ basicInfo?.productModel?.model || "-" }}
                  </view>
                  <view class="title">
                    HWID：{{ basicInfo?.bmsInfo?.hwid || "-" }}
                  </view>
                  <view class="title">
                    IMEI：{{ basicInfo?.bmsInfo?.imei || "-" }}
                  </view>
                  <view class="title">
                    软件版本：{{ basicInfo?.bmsInfo?.softwareVersion || "-" }}
                  </view>
                  <view class="title"> 出厂日期：{{ "-" }}</view>
                  <view class="title">
                    激活日期：{{
                      basicInfo.bmsInfo?.activeTime
                        ? dayjs(basicInfo.bmsInfo?.activeTime).format(
                            "YYYY/MM/DD"
                          )
                        : "-"
                    }}
                  </view>
                  <view class="title">
                    服务到期日期：{{
                      basicInfo.bmsInfo?.serviceExpireDate
                        ? dayjs(basicInfo.bmsInfo?.serviceExpireDate).format(
                            "YYYY/MM/DD"
                          )
                        : "-"
                    }}
                  </view>
                </view>
              </view>
            </view>
          </swiper-item>
          <swiper-item item-id="C">
            <view class="sw-item" id="slide-C">
              <div class="div charge-title flex justify-between">
                <div class="div charge-title-l">
                  <span class="name">昨日充电时长</span>
                  <div class="div flex items-center">
                    <span class="text-2.5xl font-bold ml-1 charge-num">
                      {{ chargeStatisticsData.yesterdayChargeDur }}
                    </span>
                    <span class="text-xs charge-unit"> h </span>
                  </div>
                </div>
                <div
                  class="div charge-title-r h-12 leading-12 rounded-r text-sm font-medium text-right"
                >
                  <span class="leading-tight text-left name">昨日放电时长</span>
                  <div class="div flex items-center">
                    <span class="text-2.5xl font-bold ml-1 charge-num">
                      {{ chargeStatisticsData.yesterdayDischargeDur }}
                    </span>
                    <span class="text-xs charge-unit">h</span>
                  </div>
                </div>
              </div>
              <view class="history">
                <view class="history-title flex justify-between items-center">
                  <view class="flex items-center">
                    <span>{{
                      chargeStatisticsData?.beforeYesterdayChargeDur == 0
                        ? "前一日无数据"
                        : "较前一日" + "："
                    }}</span>
                    <percentage
                      :num="chargeStatisticsData.comparedChargePercent"
                      class="ml-3"
                    />
                  </view>
                  <view class="flex items-center">
                    <span>{{
                      chargeStatisticsData?.beforeYesterdayDischargeDur == 0
                        ? "前一日无数据"
                        : "较前一日" + "："
                    }}</span>
                    <percentage
                      :num="chargeStatisticsData.comparedDischargePercent"
                      class="ml-3"
                    />
                  </view>
                </view>
                <view class="flex justify-between items-center px-3 total">
                  <view class="flex justify-start">
                    <view class="mr-4">
                      <view class="mb-4">月累计(h)</view>
                      <view class="b">{{
                        chargeStatisticsData.currentMonthChargeDur
                      }}</view>
                    </view>
                    <view>
                      <view class="mb-4">总计(h)</view>
                      <view class="b">
                        {{ chargeStatisticsData.totalChargeDur }}</view
                      >
                    </view>
                  </view>
                  <view class="flex justify-end text-right">
                    <view class="mr-4">
                      <view class="mb-4">月累计(h)</view>
                      <view class="b">{{
                        chargeStatisticsData.currentMonthDischargeDur
                      }}</view>
                    </view>
                    <view>
                      <view class="mb-4">总计(h)</view>
                      <view class="b">
                        {{ chargeStatisticsData.totalDischargeDur }}</view
                      >
                    </view>
                  </view>
                </view>
              </view>
              <bar width="646" height="400" />
              <view class="mt-6 pb-8">
                <view class="mb-4 px-4">运行记录</view>
                <view class="run-title">
                  <view class="run-title-time">时间</view>
                  <view class="run-title-status">运行状态</view>
                  <view class="run-title-soc">SOC</view>
                  <view class="run-title-cap">充/放电量</view>
                </view>
                <view
                  v-for="(item, index) in runningData"
                  :key="index"
                  class="run-list run-title"
                >
                  <view class="run-title-time">
                    <view>
                      {{ formatterDate(item.startTime, item.endTime) }}
                      <!-- {{ dayjs(item.startTime).format("MM/DD") }} -->
                    </view>
                    <view>{{
                      formatterTime(item.startTime, item.endTime)
                    }}</view>
                  </view>
                  <view class="run-title-status">
                    <span
                      class="y"
                      :class="item.chargeStatus === 1 ? 'green' : 'blue'"
                    ></span>
                    <span>{{ item.chargeStatus === 1 ? "充电" : "放电" }}</span>
                  </view>
                  <view class="run-title-soc"
                    >{{ item.startSoc || 0 }}% - {{ item.endSoc || 0 }}%
                  </view>
                  <view class="run-title-cap">
                    {{
                      item.chargeStatus == 1
                        ? item.chargeQuantity
                        : item.dischargeQuantity
                    }}
                  </view>
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, nextTick, computed, reactive } from "vue";
import { onShow, onUnload } from "@dcloudio/uni-app";
import carImg from "./carImg.vue";
import gauge from "./gauge.vue";
import gauge1 from "./gauge1.vue";
import { getState } from "../common/setup";
import { unitConversion, alternateUnits } from "@/common/util";
import distributed from "./distributed";
import TempDistributed from "./tempDistributed";
import bar from "./newBar.vue";
import percentage from "./percentage.vue";
import powerApi from "@/apiService/power";
import api from "@/apiService/index";
import dayjs from "dayjs";

const pageInfo = ref();
onShow(async () => {
  const pages = getCurrentPages();
  const options = pages[pages.length - 1].options;
  pageInfo.value = options;
  uni.hideHomeButton();
});
const activeKey = ref("A");
const onChange = (val, ind) => {
  console.log("val,ind", val, ind);
  activeKey.value = val;
};
const tabOptions = ref([
  {
    label: "实时状态",
    id: "A",
  },
  {
    label: "基础信息",
    id: "B",
  },
  {
    label: "运行历史",
    id: "C",
  },
]);
const scrollLeft = ref(0);
const scrollIntoView = ref("tab");
// swiper
const duration = ref(400);
const swiperHeight = ref();
const updateSwiperHeight = () => {
  const id = `#slide-${activeKey.value}`;
  nextTick(() => {
    uni
      .createSelectorQuery()
      .select(id)
      .boundingClientRect((rect) => {
        if (rect) {
          swiperHeight.value = rect.height;
        }
      })
      .exec();
  });
};
const onSwiperChange = () => {
  updateSwiperHeight();
};
const basicInfo = ref();
const getBasicInfo = async () => {
  let res = await powerApi.getBasicInfo(pageInfo.value.sn);
  basicInfo.value = res.data.data;
};
const realData = ref();
const getRealData = async () => {
  let res = await powerApi.getRealData(pageInfo.value.sn);
  realData.value = res.data.data;
};
const realCells = ref();
const getRealCells = async () => {
  let res = await powerApi.getRealCells(pageInfo.value.sn);
  realCells.value = res.data.data;
};
const vehicleTypeOptions = ref([]);
const getVehicleTypeOptions = async () => {
  let res = await api.getDictByType("vehicleType");
  vehicleTypeOptions.value = res.data.data;
};
const vehicleType = computed(() => {
  console.log(vehicleTypeOptions.value);
  console.log(basicInfo.value?.productModel?.vehicleType);
  if (!basicInfo.value?.productModel?.vehicleType) return "-";
  const item = vehicleTypeOptions.value.find(
    (item) => item.value == basicInfo.value?.productModel?.vehicleType
  );
  if (!item) return "-";
  return item.label;
});
const chargeStatisticsData = reactive({
  beforeYesterdayChargeDur: void 0,
  beforeYesterdayDischargeDur: void 0,
  comparedChargePercent: void 0,
  comparedDischargePercent: void 0,
  currentMonthChargeDur: void 0,
  currentMonthDischargeDur: void 0,
  totalChargeDur: void 0,
  totalDischargeDur: void 0,
  yesterdayChargeDur: void 0,
  yesterdayDischargeDur: void 0,
});
const statsPowerBattDurUsageSummary = async () => {
  //
  let res = await powerApi.statsPowerBattDurUsageSummary({
    sn: pageInfo.value.sn,
    projectId: basicInfo.value?.bmsInfo.projectId || undefined,
  });
  //
  Object.assign(chargeStatisticsData, res.data.data);
};
const runningData = ref([]);
const getPowerBmsChargeRecordPageList = async () => {
  let res = await powerApi.getPowerBmsChargeRecordPageList({
    size: 10,
    sn: pageInfo.value.sn,
    current: 1,
    chargeStatus: "",
  });
  runningData.value = res.data.data.records;
};
const formatterDate = (startTime, endTime) => {
  const start = dayjs(startTime).format("MMDD");
  const end = dayjs(endTime).format("MMDD");
  if (start == end) {
    return dayjs(startTime).format("MM/DD");
  } else {
    return (
      dayjs(startTime).format("MM/DD") + "-" + dayjs(endTime).format("MM/DD")
    );
  }
};
const formatterTime = (startTime, endTime) => {
  const start = dayjs(startTime).format("MMDD");
  const end = dayjs(endTime).format("MMDD");
  if (start == end) {
    return (
      dayjs(startTime).format("HH:mm") + "-" + dayjs(endTime).format("HH:mm")
    );
  } else {
    return (
      dayjs(startTime).format("HH:mm") +
      "-次日" +
      dayjs(endTime).format("HH:mm")
    );
  }
};
onMounted(async () => {
  await getVehicleTypeOptions();
  await getBasicInfo();
  await getRealData();
  await getRealCells();

  // 统计
  await statsPowerBattDurUsageSummary();
  await getPowerBmsChargeRecordPageList();
  updateSwiperHeight(); // 初始高度
});
</script>

<style lang="scss" scoped>
.web {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.tabs {
  padding: 24rpx;
  padding-bottom: 0;
  white-space: nowrap;
  display: flex;
  line-height: 40rpx;
  justify-content: space-between;
  &::after {
    display: block;
    content: "";
    width: 100%;
    height: 1px;
    background: rgba(0, 0, 0, 0.08);
    position: absolute;
    left: 0;
    bottom: 4rpx;
    z-index: 1;
  }
  .tab {
    // display: inline;
    padding-bottom: 16rpx;
    color: $uni-secondary-color;
    // flex: 1;
    text-align: center;
    position: relative;
    z-index: 2;
    &.selected {
      color: $uni-main-color;
      position: relative;
      font-weight: bold;
      &.textColor {
        color: $uni-primary;
      }
      &::after {
        content: "";
        height: 9rpx;
        width: 48rpx;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        background-color: $uni-primary;
        border-radius: 5rpx;
      }
    }
  }
}
.deviceInfo {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 38rpx 30rpx;
}
.car-img {
  width: 262rpx;
  height: 156rpx;
}
.car-info {
  flex: 1;
  text-align-last: left;
  margin-left: 68rpx;
  .no {
    font-size: 28rpx;
    color: #6fbece;
    line-height: 48rpx;
  }
  .sn {
    font-size: 24rpx;
    color: rgba(34, 34, 34, 0.8);
    line-height: 34rpx;
  }
}
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100% - 156rpx);
}
.swiper-box {
  overflow-y: auto;
  flex: 1;
}
.swiper {
  width: 750rpx;
  height: 100vh;
  overflow-y: auto;
}
.sw-item {
  padding: 0 24rpx;
}
.guage-chart {
  width: 356rpx;
  height: 300rpx;
  margin: 0 auto;
}
.real-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
}

.statistic {
  padding-top: 16rpx;
  padding-bottom: 16rpx;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  margin-top: 16rpx;
}

.statistic-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;

  .statistic-item {
    text-align: center;
    position: relative;
    // padding-bottom: 16rpx;
    width: 33.333%;

    & + .statistic-item {
      border-left: 1px solid $uni-border-1;
      margin: 0;
    }

    .num {
      font-size: 32rpx;
      line-height: 48rpx;
      font-weight: bold;
      color: #1e312b;
    }

    .name {
      font-size: 24rpx;
      line-height: 40rpx;
      color: rgba(30, 49, 43, 0.6);
    }

    &::after {
      display: block;
      content: "";
      width: 96rpx;
      height: 8rpx;
      border-radius: 4rpx;
      background: transparent;
      position: absolute;
      left: 50%;
      margin-left: -48rpx;
      bottom: 0;
    }

    &.active {
      &::after {
        background: $uni-primary;
      }
    }
  }
}

.bg-line {
  width: 100%;
  height: 28rpx;
  background: rgba(34, 34, 34, 0.08);
}

.distributed {
  padding-top: 24rpx;
  padding-bottom: 24rpx;
  background: rgba(34, 34, 34, 0.08);
}

.distributed-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;

  .distributed-item {
    text-align: center;
    position: relative;
    width: 33.333%;

    & + .distributed-item {
      border-left: 1px solid rgba(0, 0, 0, 0.08);
      margin: 0;
    }

    .num {
      font-size: 32rpx;
      line-height: 48rpx;
      font-weight: bold;
      color: #1e312b;
    }

    .name {
      font-size: 24rpx;
      line-height: 40rpx;
      color: rgba(30, 49, 43, 0.6);
    }

    &::after {
      display: block;
      content: "";
      width: 96rpx;
      height: 8rpx;
      border-radius: 4rpx;
      background: transparent;
      position: absolute;
      left: 50%;
      margin-left: -48rpx;
      bottom: 0;
    }

    &.active {
      &::after {
        background: $uni-primary;
      }
    }
  }
}
.distributed-chart {
  width: 100%;
  height: 326rpx;
}
.distributed-chart-temp {
  width: 100%;
  height: 226rpx;
}
#slide-A {
  padding-bottom: 40rpx;
  padding-top: 32rpx;
}
#slide-C {
  padding-top: 32rpx;
}
.basic-info {
  background: #f6f6f6;
  padding: 24rpx;
  margin-bottom: 24rpx;
}
.info-box {
  background: #fff;
  border-radius: 8rpx;
  padding: 24rpx 28rpx;
  & + .info-box {
    margin-top: 24rpx;
  }
  .title {
    font-size: 28rpx;
    line-height: 48rpx;
    color: rgba(34, 34, 34, 0.8);
    &.b {
      font-weight: bold;
      color: #222222;
    }
  }
}

.charge-title {
  position: relative;
  left: -6rpx;
  // margin-top: 16rpx;
  margin-bottom: 14rpx;
  display: flex;
  justify-content: space-between;
  color: #333;
  .name {
    font-size: 28rpx;
    line-height: 40rpx;
  }
  /* 基础文本样式 */
  .text-title {
    color: #333;
  }

  .dark .text-title-dark {
    color: #fff;
  }
}

.charge-title-l,
.charge-title-r {
  width: calc(50% - 38rpx);
  height: 56rpx;
  position: relative;
  display: flex;
  align-items: center;
  padding: 16rpx;
  border-radius: 8rpx 0 0 8rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.charge-title-l {
  background: rgba(51, 190, 79, 0.1);
  padding-left: 24rpx;
  justify-content: flex-start;
  border-radius: 8rpx 0 0 8rpx;

  /* 内部文本布局 */
  > .div:first-child {
    line-height: 48rpx;
    font-size: 32rpx;
  }

  /* 数字样式 */
  > .div:nth-child(2) {
    font-weight: bold;
  }

  /* 单位样式 */
  > .div:last-child {
    font-size: 24rpx;
    line-height: 40rpx;
    margin-left: 4rpx;
  }

  &::after {
    display: block;
    content: "";
    width: 0;
    height: 0;
    left: 100%;
    position: absolute;
    top: 0;
    border-top: 56rpx solid rgba(51, 190, 79, 0.1);
    border-right: 68rpx solid transparent;
  }
}

.charge-title-r {
  background: rgba(119, 155, 219, 0.1);
  padding-right: 24rpx;
  justify-content: flex-end;
  border-radius: 0 8rpx 8rpx 0;

  /* 内部文本布局 */
  > .div:first-child {
    line-height: 48rpx;
    font-size: 32rpx;
  }

  /* 数字样式 */
  > .div:nth-child(2) {
    font-size: 70rpx;
    line-height: 64rpx;
    font-weight: bold;
    margin-left: 36rpx;
  }

  /* 单位样式 */
  > .div:last-child {
    font-size: 24rpx;
    line-height: 40rpx;
    margin-left: 4rpx;
  }

  &::after {
    display: block;
    content: "";
    width: 0;
    height: 0;
    right: 100%;
    position: absolute;
    top: 0;
    border-bottom: 56rpx solid rgba(119, 155, 219, 0.1);
    border-left: 68rpx solid transparent;
  }
}

/* 保留原有的数字和单位样式类 */
.charge-num {
  letter-spacing: 0rpx;
  line-height: 1;
}

.charge-unit {
  line-height: 56rpx;
}
.history {
  font-size: 24rpx;
  margin-bottom: 24rpx;
}
.history-title {
  padding: 20rpx 12rpx;
  border-bottom: 1px solid rgba(119, 155, 219, 0.1);
  margin-bottom: 12rpx;
}
.run-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: rgba(34, 34, 34, 0.65);
  font-size: 24rpx;
  line-height: 32rpx;
  padding: 0 16rpx;
  .run-title-time {
    width: 200rpx;
    text-align: left;
  }
  .run-title-status {
    width: 150rpx;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    .y {
      width: 20rpx;
      height: 20rpx;
      display: block;
      border-radius: 50%;
      background: transparent;
      box-shadow: 0px 0px 8px 0px rgba(51, 190, 79, 0.2);
      border: 2px solid #ffffff;
      margin-right: 12rpx;
      &.blue {
        background: #4b82ff;
      }
      &.green {
        background: #33be4f;
      }
    }
  }
  .run-title-soc {
    width: 190rpx;
    text-align: center;
  }
  .run-title-cap {
    width: 160rpx;
    text-align: right;
  }
}
.run-list {
  padding: 20rpx;
  border-radius: 16rpx;
  background: rgba(34, 34, 34, 0.04);
  margin-top: 16rpx;
}

.iconSvg {
  width: 28rpx;
  height: 28rpx;
  margin-left: 12rpx;
}
.dis-title {
  color: $uni-base-color;
}
.dis-sub-title {
  color: $uni-secondary-color;
}
.total {
  color: rgba(34, 34, 34, 0.8);
  .b {
    font-weight: medium;
  }
}
</style>