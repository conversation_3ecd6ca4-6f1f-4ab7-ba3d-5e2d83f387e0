<template>
  <view class="chart-container">
    <!-- 顶部控制栏 -->
    <view class="chart-header">
      <!-- 左侧视图切换按钮 -->
      <view class="view-switch">
        <view
          class="switch-item"
          :class="{ active: currentView === 'capacity' }"
          @click="switchView(currentView)"
        >
          <text class="switch-text">
            {{ currentView === "capacity" ? "充放电电量" : "充放电时长" }}</text
          >

          <image src="@/static/toggle.png" class="toggle" />
        </view>
      </view>

      <!-- 右侧条件筛选下拉框 -->
      <view class="filter-dropdown">
        <picker
          :value="currentPeriodIndex"
          :range="periodOptions"
          @change="onPeriodChange"
          range-key="label"
        >
          <view class="picker-display">
            <text>{{
              periodOptions[currentPeriodIndex].label || "请选择"
            }}</text>
            <text class="arrow-icon">▼</text>
          </view>
        </picker>
      </view>
    </view>

    <!-- 图表容器 -->
    <view class="chart-wrapper">
      <!-- #ifdef MP-WEIXIN -->
      <l-echart
        ref="chartRef"
        @finished="onEChartReady"
        class="chart-inner"
      ></l-echart>
      <!-- #endif -->
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, getCurrentInstance } from "vue";
import powerApi from "@/apiService/power";
import dayjs from "dayjs";

// const instance = getCurrentInstance();

// Props
const props = defineProps({
  width: {
    type: [String, Number],
    default: "100%",
  },
  height: {
    type: [String, Number],
    default: 190,
  },
});

// ECharts import
//#ifdef MP-WEIXIN
// const echarts = require("./echarts.min.js");
let echarts;
try {
  // 尝试从本地目录加载
  echarts = require("./echarts.min.js");
} catch (error) {
  console.error("Failed to load echarts from local:", error);
  // 尝试从uni_modules加载
  try {
    echarts = require("../uni_modules/lime-echart/static/echarts.min.js");
  } catch (error2) {
    console.error("Failed to load echarts from uni_modules:", error2);
  }
}

//#endif

// 响应式数据
const chartInstance = ref(null);
const chartRef = ref();
const currentView = ref("capacity"); // 'capacity' | 'duration'
const currentPeriodIndex = ref(0);
const chartData = ref([]);
const loading = ref(false);

// 时间段选项配置
const periodOptions = ref([
  {
    label: "近七日",
    value: "week",
  },
  // {
  //   label: "近30天",
  //   value: "month",
  // },
  {
    label: "近一年",
    value: "year",
  },
]);

// 根据当前时间计算参数
const getRequestParams = () => {
  const currentOption = periodOptions.value[currentPeriodIndex.value];
  if (currentOption.value === "week") {
    return {
      startDate: dayjs().subtract(6, "day").format("YYYY-MM-DD"),
      endDate: dayjs().format("YYYY-MM-DD"),
      periodType: "day",
    };
  } else if (currentOption.value === "month") {
    return {
      startDate: dayjs().subtract(29, "day").format("YYYY-MM-DD"),
      endDate: dayjs().format("YYYY-MM-DD"),
      periodType: "day",
    };
  } else {
    return {
      startMonth: dayjs().subtract(11, "month").format("YYYY-MM"),
      endMonth: dayjs().format("YYYY-MM"),
      periodType: "month",
    };
  }
};

// 格式化日期显示
const formatDate = (dateStr, periodType) => {
  if (!dateStr) return "";

  const date = new Date(dateStr);
  if (periodType !== "month") {
    // MM/DD 格式
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${month}/${day}`;
  } else if (periodType === "month") {
    // MM 格式
    const month = String(date.getMonth() + 1).padStart(2, "0");
    return month;
  }
  return dateStr;
};

// 创建图表配置
const createChartOptions = () => {
  const currentPeriod = periodOptions.value[currentPeriodIndex.value];
  const isCapacityView = currentView.value === "capacity";
  // 处理数据
  const xAxisData = chartData.value.map((item) =>
    formatDate(item.date, currentPeriod.value)
  );

  const chargeData = chartData.value.map((item) =>
    isCapacityView ? item.chargeCap : item.chargeDur
  );

  const dischargeData = chartData.value.map((item) =>
    isCapacityView ? item.dischargeCap : item.dischargeDur
  );
  const unit = isCapacityView ? "kWh" : "h";
  const yAxisName = isCapacityView ? "电量(kWh)" : "时长(h)";
  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((param) => {
          result += `${param.marker}${param.seriesName}: ${
            param.value || 0
          }${unit}<br/>`;
        });
        return result;
      },
    },
    legend: {
      data: ["充电", "放电"],
      top: 0,
      textStyle: {
        color: "#333",
        fontSize: 12,
      },
      itemWidth: 10,
      itemHeight: 10,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "32px",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: xAxisData,
      axisLabel: {
        color: "#666",
        fontSize: 10,
      },
      axisLine: {
        lineStyle: {
          color: "#e0e0e0",
        },
      },
      // splitNumber: 5,
      interval: xAxisData.length > 13 ? 5 : 1,
    },
    yAxis: {
      type: "value",
      name: yAxisName,
      nameTextStyle: {
        color: "#666",
        fontSize: 10,
      },
      axisLabel: {
        color: "#666",
        fontSize: 10,
      },
      axisLine: {
        lineStyle: {
          color: "#e0e0e0",
        },
      },
      splitLine: {
        lineStyle: {
          color: "#f0f0f0",
        },
      },
    },
    series: [
      {
        name: "充电",
        type: "bar",
        data: chargeData,
        itemStyle: {
          color: "#33BE4F",
        },
        barWidth: "30%",
      },
      {
        name: "放电",
        type: "bar",
        data: dischargeData,
        itemStyle: {
          color: "#779BDB",
        },
        barWidth: "30%",
      },
    ],
  };
};

const chartOptions = {
  tooltip: {
    show: false,
    // trigger: "axis",
    // axisPointer: {
    //   type: "shadow",
    // },
    // formatter: function (params) {
    //   let result = `${params[0].axisValue}<br/>`;
    //   params.forEach((param) => {
    //     result += `${param.marker}${param.seriesName}: ${
    //       param.value || 0
    //     }${unit}<br/>`;
    //   });
    //   return result;
    // },
  },
  legend: {
    data: ["充电", "放电"],
    top: 10,
    textStyle: {
      color: "#333",
      fontSize: 12,
    },
  },
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    top: "15%",
    containLabel: true,
  },
  xAxis: {
    type: "category",
    data: [],
    axisLabel: {
      color: "#666",
      fontSize: 10,
    },
    axisLine: {
      lineStyle: {
        color: "#e0e0e0",
      },
    },
  },
  yAxis: {
    type: "value",
    name: "",
    nameTextStyle: {
      color: "#666",
      fontSize: 10,
    },
    axisLabel: {
      color: "#666",
      fontSize: 10,
    },
    axisLine: {
      lineStyle: {
        color: "#e0e0e0",
      },
    },
    splitLine: {
      lineStyle: {
        color: "#f0f0f0",
      },
    },
  },
  series: [
    {
      name: "充电",
      type: "bar",
      data: [],
      itemStyle: {
        color: "#33BE4F",
      },
      barWidth: "30%",
    },
    {
      name: "放电",
      type: "bar",
      data: [],
      itemStyle: {
        color: "#779BDB",
      },
      barWidth: "30%",
    },
  ],
};
// 模拟API调用
const fetchChartData = async () => {
  loading.value = true;
  try {
    const requestParams = getRequestParams();
    console.log("请求参数:", requestParams);
    // 模拟接口调用
    // 模拟返回数据
    const mockData = await powerApi.statsPowerBattDailyUsage({
      ...requestParams,
      sn: "866597074507136",
      projectId: "1930088216192688129",
    });
    chartData.value = mockData.data.data;
    // 重新渲染图表
    updateChart();
  } catch (error) {
    console.error("获取图表数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 视图切换
const switchView = (view) => {
  console.log("[ view ] >", view, currentView.value);
  if (view == "capacity") {
    currentView.value = "duration";
  } else {
    currentView.value = "capacity";
  }
  updateChart();
};

// 时间段切换
const onPeriodChange = (e) => {
  currentPeriodIndex.value = e.detail.value;
  fetchChartData(); // 切换时间段需要重新获取数据
};

// 更新图表
const updateChart = () => {
  console.log("[ chartData.value ] >", chartData.value);
  if (!chartInstance.value) {
    console.warn("图表实例不存在，无法更新");
    return;
  }
  if (!chartData.value || chartData.value.length === 0) {
    console.warn("图表数据为空，无法更新");
    return;
  }
  try {
    const options = createChartOptions();
    console.log("[ options ] >", options);
    chartRef.value && chartRef.value.dispose();
    chartRef.value?.init(echarts, (chart) => {
      if (chart) {
        chartInstance.value = chart;
        console.log("微信小程序柱状图2次初始化成功");
        chartInstance.value.setOption(options);
        fetchChartData();
      }
    });
    // nextTick(() => {
    //   chartInstance.value.setOption(chartOptions);
    //   instance.proxy.$forceUpdate();
    // });
    console.log("✅ 图表更新成功");
  } catch (error) {
    console.error("❌ 图表更新失败:", error);
  }
};
// #ifdef MP-WEIXIN
function onEChartReady() {
  console.log("[ newBar onEChartReady triggered ✅ ]");
  try {
    if (!echarts) {
      console.error("ECharts not loaded");
      return;
    }
    chartRef.value?.init(echarts, (chart) => {
      if (chart) {
        chartInstance.value = chart;
        console.log("微信小程序柱状图初始化成功");
        fetchChartData();
      }
    });
  } catch (error) {
    console.error("微信小程序柱状图初始化失败:", error);
  }
}
//#endif

// 组件挂载
onMounted(() => {
  console.log("newBar组件已挂载");
  console.log("periodOptions:", periodOptions.value);
  console.log("currentPeriodIndex:", currentPeriodIndex.value);

  // #ifdef MP-WEIXIN
  console.log("微信小程序环境，等待onEChartReady");
  //#endif
});

// 暴露方法供外部调用
defineExpose({
  refreshData: fetchChartData,
  switchView,
  updateChart, // 暴露updateChart方法供外部调用
  getCurrentView: () => currentView.value,
  getCurrentPeriod: () => periodOptions.value[currentPeriodIndex.value],
  getChartData: () => chartData.value,
  getChartInstance: () => chartInstance.value,
});
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  background: rgba(34, 34, 34, 0.04);
  border-radius: 16rpx;
  overflow: hidden;
  padding: 24rpx;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.view-switch {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.switch-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  .toggle {
    width: 40rpx;
    height: 40rpx;
    object-fit: contain;
    margin-left: 4rpx;
  }
  .switch-text {
    font-size: 28rpx;
    color: #222222;
    transition: color 0.3s ease;
  }
}

.filter-dropdown {
  .picker-display {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 12rpx 16rpx;
    background: #f6f7f7;
    border-radius: 28px;
    min-width: 120rpx;
    cursor: pointer;

    text {
      font-size: 28rpx;
      color: #333;
    }

    .arrow-icon {
      font-size: 20rpx;
      color: #999;
      transition: transform 0.3s ease;
    }

    &:active {
      background: #f0f0f0;

      .arrow-icon {
        transform: rotate(180deg);
      }
    }
  }
}

.chart-wrapper {
  width: 100%;
  height: 380rpx; /* 190px * 2 */
}

.chart-inner {
  width: 100%;
  height: 100%;
  background: #fff;
}

/* H5图表容器 */
#chargeDischargeChart {
  width: 100%;
  height: 100%;
  min-height: 350rpx;
}
</style>
