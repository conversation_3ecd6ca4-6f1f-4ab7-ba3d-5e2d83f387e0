<template>
  <view class="w-full h-full">
    <EchartWrapper
      chart-id="tempDistributedChart"
      @ready="onChartReady"
      class="chart-container"
    />
  </view>
</template>

<script setup>
import { ref, watch } from "vue";
import EchartWrapper from "./EchartWrapper.vue";
// 定义响应式变量
const chartInstance = ref(null);
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
});
// 创建图表配置
const createChartOptions = (data, min, max) => {
  return {
    gradientColor: ["#D4F6FB", "#A4DBE6", "#6FBECE", "#5FA7BA", "#204765"],
    tooltip: {
      show: true,
      position: "top",
      formatter: function (params) {
        return (
          params.dataIndex +
          1 +
          "号电芯" +
          params.seriesName +
          "<br />" +
          params.marker +
          "    " +
          params.value[2]
        );
      },
    },
    grid: {
      height: "88px",
      width: "96%",
      top: "10%",
      left: "2%",
    },
    xAxis: {
      type: "category",
      data: [],
      splitArea: {
        show: true,
      },
    },
    yAxis: {
      type: "category",
      data: [],
      splitArea: {
        show: true,
      },
      show: false,
    },
    visualMap: {
      min: min,
      max: max,
      calculable: true,
      orient: "horizontal",
      left: "center",
      bottom: "0",
      precision: 3,
      textStyle: {
        color: "rgba(34, 34, 34, 0.6)",
      },
    },
    series: [
      {
        name: "电芯电压",
        type: "heatmap",
        data: data,
        label: {
          show: true,
          color: "transparent",
        },
        itemStyle: {
          color: "transparent",
          borderColor: "#ffffff",
          borderWidth: 1,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  };
};
// 数据转换函数
const transformData3 = (data, yCount) => {
  // 验证数据总量是否匹配
  const result = [];
  let xCount = Math.floor(data.length / yCount);
  console.log("xCount", xCount);
  if (data.length !== xCount * yCount) {
    throw new Error("数据总量与坐标数量不匹配");
  }

  // 按y轴数量分割数据
  for (let y = 0; y < yCount; y++) {
    // 计算当前y轴数据切片范围
    const start = y * xCount;
    const end = start + xCount;
    const yData = data.slice(start, end);
    // 生成坐标数据
    yData.forEach((value, x) => {
      result.push([x, y, value]);
    });
  }

  return result;
};

// 图表准备就绪回调
const onChartReady = (chart) => {
  console.log("[ distributed chart ready ✅ ]");
  chartInstance.value = chart;
};

const updateChart = () => {
  if (!props.data || props.data.length === 0) {
    throw new Error("数据不能为空");
  }
  const newVs = props.data;
  let min = Math.min(...newVs);
  let max = Math.max(...newVs);
  const res = transformData3(newVs, 2);
  const options = createChartOptions(res, min, max);
  chartInstance.value.setOption(options);
};
// 暴露组件方法供外部调用
defineExpose({
  getChartInstance: () => chartInstance.value,
});
watch(
  () => props.data,
  (val) => {
    if (val && val.length) {
      updateChart();
    }
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 100%;
  min-height: 200px;
}

#tempDistributedChart {
  width: 100%;
  height: 100%;
}

/* 微信小程序特殊样式 */
/* #ifdef MP-WEIXIN */
.chart-container {
  background-color: #fff;
}
/* #endif */
</style>