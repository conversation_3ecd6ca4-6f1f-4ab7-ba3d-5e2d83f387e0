<template>
  <view class="chart-wrapper">
    <!-- #ifdef WEB -->
    <view :id="chartId" :ref="chartId" class="chart-inner"></view>
    <!-- #endif -->
    <!-- #ifdef MP-WEIXIN -->
    <l-echart
      ref="chartRef"
      @finished="onEChartReady"
      class="chart-inner"
    ></l-echart>
    <!-- #endif -->
  </view>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
// Props
const props = defineProps({
  chartId: {
    type: String,
    default: "chart",
  },
});

// Emits
const emit = defineEmits(["ready"]);

// Refs
const chartRef = ref();
const chartInstance = ref(null);

// ECharts import
//#ifdef WEB
import * as echarts from "echarts";
//#endif
//#ifdef MP-WEIXIN
let echarts;
try {
  // 尝试从本地目录加载
  echarts = require("./echarts.min.js");
} catch (error) {
  console.error("Failed to load echarts from local:", error);
  // 尝试从uni_modules加载
  try {
    echarts = require("../uni_modules/lime-echart/static/echarts.min.js");
  } catch (error2) {
    console.error("Failed to load echarts from uni_modules:", error2);
  }
}
//#endif

// Methods
const initWebChart = () => {
  // #ifdef WEB
  const ElBox = document.getElementById(props.chartId);
  if (ElBox) {
    nextTick(() => {
      const chart = echarts.init(ElBox);
      chartInstance.value = chart;
      emit("ready", chart);
    });
  }
  //#endif
};

// #ifdef MP-WEIXIN
function onEChartReady() {
  console.log("[ EchartWrapper onEChartReady triggered ✅ ]");

  try {
    // 确保echarts已加载
    if (!echarts) {
      console.error("ECharts not loaded");
      return;
    }

    chartRef.value?.init(echarts, (chart) => {
      if (chart) {
        chartInstance.value = chart;
        emit("ready", chart);
        console.log("微信小程序图表初始化成功");
      }
    });
  } catch (error) {
    console.error("微信小程序图表初始化失败:", error);
  }
}
//#endif

// Lifecycle
onMounted(() => {
  // #ifdef WEB
  initWebChart();
  //#endif
});

// Expose methods
defineExpose({
  getChartInstance: () => chartInstance.value,
  setOption: (option) => {
    if (chartInstance.value) {
      chartInstance.value.setOption(option);
    }
  },
  resize: () => {
    if (chartInstance.value) {
      chartInstance.value.resize();
    }
  },
});
</script>

<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}

.chart-inner {
  width: 100%;
  height: 100%;
}
</style>
