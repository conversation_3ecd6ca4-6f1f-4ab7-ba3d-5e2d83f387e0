import { createRouter, createWebHashHistory } from 'vue-router'
import store from '@/store'
import Cookies from 'js-cookie'
import { KeepAlive } from 'vue'
// import service from '@/apiService'
// import { message } from 'ant-design-vue'

export const routes = [
    {
        path: '/device',
        name: 'layout',
        breadcrumbName: 'layout',
        component: () => import('@/views/layout.vue'),
        redirect: '/device',
        children: [
            {
                path: '/device/alarmOverview',
                name: 'alarmOverview',
                component: () => import('@/views/device/alarmOverview.vue'),
            },

            {
                path: '/rolePage',
                name: '系统设置',
                component: () => import('@/views/role/index.vue'),
            },
            {
                path: '/operation',
                name: 'operation',
                component: () => import('@/views/operation/index.vue'),
            },
            {
                path: '/operation-car',
                name: 'operationCar',
                component: () => import('@/views/device/car/operation/index.vue'),
                redirect: '/operation-car/project',
                children:[
                    {
                        path: '/operation-car/device',
                        name: 'operationCarDevice',
                        component: () => import('@/views/device/car/operation/device.vue'),
                    },
                    {
                        path: '/operation-car/project',
                        name: 'operationCarProject',
                        component: () => import('@/views/device/car/operation/project.vue'),
                    },
                    {
                        path: '/operation-car/alarm',
                        name: 'operationCarAlarm',
                        component: () => import('@/views/device/car/operation/alarm.vue'),
                    },
                ]
            },
            {
                path: '/user',
                name: 'user',
                component: () => import('@/views/user/index.vue'),
            },
            {
                path: '/device',
                name: 'deviceOverview',
                component: () => import('@/views/device/siteOverview.vue'),
                meta: {
                    keepAlive: true,
                    isFullPage: true
                }
            },
            {
                path: '/device/deviceDetail',
                name: 'deviceDetail',
                component: () => import('@/views/device/deviceDetail.vue'),
            },
            {
                path: '/car',
                name: 'carOverview',
                component: () => import('@/views/device/car/siteOverviewCar.vue'),
                meta: {
                    keepAlive: true,
                    isFullPage: true
                }
            },
            {
                path: '/carSystem/detail',
                name: 'carSystemDetail',
                component: () => import('@/views/device/car/detail.vue'),
            },
            {
                
                path: '/carSystem/equipmentDetail',
                name: 'equipmentDetail',
                component: () => import('@/views/device/car/equipmentDetail.vue'),
            }
        ],
    },
    {
        path: '/strategy',
        name: 'strategy',
        component: () => import('@/views/strategy/index.vue'),
        redirect: '/strategy/energy',
        children: [
            {
                path: '/strategy/energy',
                name: 'energy',
                component: () => import('@/views/strategy/energy.vue'),
                meta: { showMenu: true },
            },
            {
                path: '/strategy/price',
                name: 'price',
                component: () => import('@/views/strategy/price/index.vue'),
                meta: { showMenu: true },
            },
            {
                path: '/strategy/ring',
                name: 'ring',
                component: () => import('@/views/strategy/ring/index.vue'),
                meta: { showMenu: true },
            },
            {
                path: '/strategy/models',
                name: 'models',
                component: () => import('@/views/strategy/models.vue'),
            },
        ]
    },
    {
        path: '/fullScreen/device',
        name: 'fullScreenDevice',
        component: () => import('@/views/fullScreen/device.vue'),
    },
    {
        path: '/packageWeb/index',
        name: 'packageWeb',
        component: () => import('@/views/packageWeb/index.vue'),
    },
    {
        path: '/login',
        component: () => import('@/views/auth/loginAndRegister.vue'),
        meta: {
            title: '登录-明我企服',
            dontNeedBuy: true,
            headerBg: '#fff',
        },
        children: [
            {
                path: '',
                name: 'login',
                component: () => import('@/views/auth/login.vue'),
            },

        ],
    },

    {
        path: '/agreement',
        name: 'agreement',
        meta: {
            dontNeedBuy: true,
        },
        component: () => import('@/views/auth/agreement.vue'),
    },
    {
        path: "/merchant-agreement",
        name: "merchant-agreement",
        component: () => import("@/views/agreement/merchant.vue"),
        meta: {
            dontNeedBuy: true,
        },
    },
    {
        path: '/:pathMatch(.*)*',
        name: 'noFind',
    },
]
const permissionRoutes = {
    //设置
    ES_SETTING_MANAGER: [

    ],
    //渠道
    ES_AUTHORIZATION_MANAGER: [

    ],
    //订单管理
    ES_ORDER_MANAGER: [

    ],
    //商品管理
    ES_GOODS_MANAGER: [

    ],
    //服务管理
    ES_SERVICE_MANAGER: [

    ],
    ES_COOPERATEORDER_MANAGER: [

    ],
    // 文章管理
    ES_ARTICLE_MANAGER: [

    ],
    //客户管理
    ES_CUSTOMER_MANAGER: [

    ],
    //营销管理
    ES_MARKETING_MANAGER: [

    ],
    ES_CASE_MANAGER: [

    ],
}
const router = createRouter({
    history: createWebHashHistory(),
    routes,
    scrollBehavior(to, from, savedPosition) {
        return { x: 0, y: 0 }
    },
})
export const permissionRouter = (merchantEditionType, roleCodes) => {
    let menuKeys = []
    if (merchantEditionType == 'PERSONAL') {
        //如果是个人版，除了设置菜单其他都有
        menuKeys = Object.keys(permissionRoutes).filter(
            (p) => p != 'ES_SETTING_MANAGER'
        )
    } else if (merchantEditionType == 'ENTERPRISE') {
        menuKeys =
            roleCodes.indexOf('ES_SUPPLIER_MANAGER') >= 0
                ? Object.keys(permissionRoutes)
                : roleCodes
    }

    menuKeys.forEach((key) => {
        permissionRoutes[key].forEach((item) => {
            router.addRoute('layout', item)
        })
    })
}
//先不做动态路由了，刷新的时候有问题
Object.keys(permissionRoutes).forEach((key) => {
    permissionRoutes[key].forEach((item) => {
        router.addRoute('layout', item)
    })
})

router.beforeEach(async function (to, from, next) {
    try {
        await store.commit('user/setMenu')
        const userInfoData = store?.state?.user?.userInfoData
        const token = Cookies.get('token')
        if (token && to.path !== '/login' && to.path !== '/agreement') {
            if (!userInfoData) {
                await store.dispatch('user/setUserInfoData')
            }
            if (to.name == 'noFind') {
                router.push({ path: '/device' })
            }
        }
        next()
    } catch (error) {
        next()
    }
})

// router.afterEach((to, from, next) => {
// let bodySrcollTop = document.body.scrollTop;
// if (bodySrcollTop !== 0) {
//   document.body.scrollTop = 0;
//   return;
// }
// let docSrcollTop = document.documentElement.scrollTop;
// if (docSrcollTop !== 0) {
//   document.documentElement.scrollTop = 0;
// }
// });

// const router = createRouter({
//   history: createWebHashHistory(),
//   routes,
//   scrollBehavior(to, from, savedPosition) {
//     return { x: 0, y: 0 };
//   },
// });
router.onError((error) => {
    // console.log(error,'error')
    // 路由异步加载出现error：ChunkLoadError: Loading chunk about failed, 重新加载一次页面
    const pattern = /ChunkLoadError: Loading chunk/g
    const isChunkLoadFailed = error.toString().match(pattern)
    // const targetPath = router.options.history.location;
    if (isChunkLoadFailed) {
        location.reload()
    }
})
export default router
